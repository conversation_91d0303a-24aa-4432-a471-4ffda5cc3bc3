<template>
  <div class="document-parse-status-container">
    <!-- 解析状态概览 -->
    <div class="status-overview">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>解析状态概览</span>
            <el-button
              type="text"
              :icon="Refresh"
              @click="refreshStatus"
              :loading="refreshing"
            >
              刷新
            </el-button>
          </div>
        </template>
        
        <div class="status-stats">
          <div class="stat-item">
            <div class="stat-value">{{ statusStats.total }}</div>
            <div class="stat-label">总文档数</div>
          </div>
          <div class="stat-item">
            <div class="stat-value success">{{ statusStats.parsed }}</div>
            <div class="stat-label">已解析</div>
          </div>
          <div class="stat-item">
            <div class="stat-value warning">{{ statusStats.parsing }}</div>
            <div class="stat-label">解析中</div>
          </div>
          <div class="stat-item">
            <div class="stat-value info">{{ statusStats.uploaded }}</div>
            <div class="stat-label">待解析</div>
          </div>
          <div class="stat-item">
            <div class="stat-value danger">{{ statusStats.failed }}</div>
            <div class="stat-label">解析失败</div>
          </div>
        </div>
      </el-card>
    </div>
    
    <!-- 解析进度列表 -->
    <div class="parse-progress-list">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>解析进度</span>
            <div class="header-actions">
              <el-button
                type="primary"
                :icon="VideoPlay"
                @click="startBatchParsing"
                :disabled="!canStartBatch"
                :loading="batchStarting"
              >
                批量解析
              </el-button>
              <el-button
                type="warning"
                :icon="VideoPause"
                @click="stopBatchParsing"
                :disabled="!canStopBatch"
                :loading="batchStopping"
              >
                停止全部
              </el-button>
            </div>
          </div>
        </template>
        
        <!-- 筛选器 -->
        <div class="filter-bar">
          <el-select
            v-model="statusFilter"
            placeholder="状态筛选"
            clearable
            @change="handleFilterChange"
            style="width: 150px"
          >
            <el-option
              v-for="status in statusOptions"
              :key="status.value"
              :label="status.label"
              :value="status.value"
            />
          </el-select>
          
          <el-input
            v-model="searchKeyword"
            placeholder="搜索文档名称"
            :prefix-icon="Search"
            clearable
            @input="handleSearch"
            style="width: 250px"
          />
        </div>
        
        <!-- 进度列表 -->
        <div class="progress-list">
          <div
            v-for="doc in filteredDocuments"
            :key="doc.id"
            class="progress-item"
            :class="{ 'parsing': doc.status === 'parsing' }"
          >
            <div class="document-info">
              <div class="document-icon">
                <el-icon :size="20" :color="getFileIconColor(doc.type || '')">
                  <Document />
                </el-icon>
              </div>
              <div class="document-details">
                <div class="document-name">{{ doc.name }}</div>
                <div class="document-meta">
                  {{ formatFileSize(doc.size || 0) }} • {{ getFileTypeLabel(doc.type || '') }}
                </div>
              </div>
            </div>
            
            <div class="parse-info">
              <div class="status-section">
                <el-tag
                  :type="getStatusTagType(doc.status || '')"
                  size="small"
                >
                  {{ getStatusLabel(doc.status || '') }}
                </el-tag>
                
                <div v-if="doc.status === 'parsing'" class="progress-section">
                  <el-progress
                    :percentage="Math.round((doc.progress || 0) * 100)"
                    :stroke-width="6"
                    :show-text="false"
                  />
                  <span class="progress-text">
                    {{ Math.round((doc.progress || 0) * 100) }}%
                  </span>
                </div>
                
                <div v-else-if="doc.status === 'parsed'" class="result-section">
                  <div class="result-item">
                    <el-icon><Grid /></el-icon>
                    <span>{{ doc.chunk_num || 0 }} 分块</span>
                  </div>
                  <div class="result-item">
                    <el-icon><Coin /></el-icon>
                    <span>{{ formatNumber(doc.token_num || 0) }} Token</span>
                  </div>
                </div>
                
                <div v-else-if="doc.status === 'failed'" class="error-section">
                  <el-tooltip :content="doc.error_msg || '解析失败'" placement="top">
                    <el-icon color="#F56C6C"><Warning /></el-icon>
                  </el-tooltip>
                  <span class="error-text">{{ doc.error_msg || '解析失败' }}</span>
                </div>
              </div>
              
              <div class="time-section">
                <div class="time-item">
                  <span class="time-label">创建:</span>
                  <span class="time-value">{{ formatDateTime(doc.create_time || '') }}</span>
                </div>
                <div v-if="doc.update_time" class="time-item">
                  <span class="time-label">更新:</span>
                  <span class="time-value">{{ formatDateTime(doc.update_time) }}</span>
                </div>
              </div>
            </div>
            
            <div class="action-section">
              <el-button
                v-if="doc.status === 'uploaded' || doc.status === 'failed'"
                type="primary"
                size="small"
                :icon="VideoPlay"
                @click="startParsing(doc)"
                :loading="doc.starting"
              >
                开始解析
              </el-button>
              
              <el-button
                v-if="doc.status === 'parsing'"
                type="warning"
                size="small"
                :icon="VideoPause"
                @click="stopParsing(doc)"
                :loading="doc.stopping"
              >
                停止解析
              </el-button>
              
              <el-button
                v-if="doc.status === 'parsed'"
                type="success"
                size="small"
                :icon="View"
                @click="viewResult(doc)"
              >
                查看结果
              </el-button>
              
              <el-dropdown
                v-if="doc.status !== 'parsing'"
                @command="(command: string) => handleAction(command, doc)"
              >
                <el-button type="text" size="small">
                  更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="reparse">重新解析</el-dropdown-item>
                    <el-dropdown-item command="config">解析配置</el-dropdown-item>
                    <el-dropdown-item command="log">查看日志</el-dropdown-item>
                    <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
          
          <!-- 空状态 -->
          <div v-if="filteredDocuments.length === 0" class="empty-state">
            <el-empty description="暂无文档数据" />
          </div>
        </div>
      </el-card>
    </div>
    
    <!-- 解析配置对话框 -->
    <el-dialog
      v-model="configDialogVisible"
      title="解析配置"
      width="600px"
    >
      <el-form :model="parseConfig" label-width="120px">
        <el-form-item label="解析器类型">
          <el-select v-model="parseConfig.parser_id" style="width: 100%">
            <el-option
              v-for="parser in parserOptions"
              :key="parser.value"
              :label="parser.label"
              :value="parser.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="分块大小">
          <el-input-number
            v-model="parseConfig.chunk_token_count"
            :min="64"
            :max="1024"
            :step="64"
          />
          <span class="form-hint">Token数量，建议128-512</span>
        </el-form-item>
        
        <el-form-item label="布局识别">
          <el-switch v-model="parseConfig.layout_recognize" />
          <span class="form-hint">是否识别文档布局结构</span>
        </el-form-item>
        
        <el-form-item label="分隔符">
          <el-input
            v-model="parseConfig.delimiter"
            placeholder="\\n!?。；！？"
          />
          <span class="form-hint">用于分割文本的分隔符</span>
        </el-form-item>
        
        <el-form-item label="页面大小">
          <el-input-number
            v-model="parseConfig.task_page_size"
            :min="1"
            :max="50"
          />
          <span class="form-hint">每次处理的页面数量</span>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="configDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveConfig" :loading="configSaving">
          保存并解析
        </el-button>
      </template>
    </el-dialog>
    
    <!-- 解析日志对话框 -->
    <el-dialog
      v-model="logDialogVisible"
      title="解析日志"
      width="80%"
    >
      <div class="log-container">
        <div class="log-header">
          <el-button :icon="Refresh" @click="refreshLog" :loading="logLoading">
            刷新日志
          </el-button>
          <el-button :icon="Download" @click="downloadLog">
            下载日志
          </el-button>
        </div>
        
        <div class="log-content">
          <pre v-if="parseLog" class="log-text">{{ parseLog }}</pre>
          <el-empty v-else description="暂无日志数据" />
        </div>
      </div>
    </el-dialog>
    
    <!-- 解析结果对话框 -->
    <el-dialog
      v-model="resultDialogVisible"
      :title="`解析结果 - ${currentResult?.name || '文档'}`"
      width="80%"
      :close-on-click-modal="false"
    >
      <div class="result-container">
        <!-- 文档基本信息 -->
        <div class="result-summary">
          <el-descriptions :column="3" border>
            <el-descriptions-item label="文档名称">
              {{ currentResult?.name || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="分块数量">
              {{ resultChunks.length || currentResult?.chunk_num || 0 }}
            </el-descriptions-item>
            <el-descriptions-item label="Token数量">
              {{ formatNumber(currentResult?.token_num || 0) }}
            </el-descriptions-item>
            <el-descriptions-item label="文档大小">
              {{ currentResult?.size ? formatFileSize(currentResult.size) : '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="解析状态">
              <el-tag :type="getStatusTagType(currentResult?.status || '')">
                {{ getStatusLabel(currentResult?.status || '') }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="解析时长">
              {{ calculateDuration(currentResult) }}
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 分块内容 -->
        <div class="result-chunks">
          <div class="chunks-header">
            <h4>文档分块内容</h4>
            <div class="chunks-actions">
              <el-button
                size="small"
                @click="toggleSelectMode"
                :type="isSelectMode ? 'primary' : 'default'"
                :icon="Select"
              >
                {{ isSelectMode ? '退出选择' : '批量选择' }}
              </el-button>
              <el-button
                v-if="isSelectMode"
                size="small"
                @click="toggleSelectAll"
                :icon="CopyDocument"
              >
                {{ selectedChunks.size === resultChunks.length ? '取消全选' : '全选' }}
              </el-button>
              <el-button
                v-if="isSelectMode && selectedChunks.size > 0"
                size="small"
                type="danger"
                @click="batchDeleteChunks"
                :loading="batchOperationLoading"
                :icon="Delete"
              >
                删除选中 ({{ selectedChunks.size }})
              </el-button>
              <el-button
                size="small"
                @click="showAddChunkDialog()"
                type="primary"
                :icon="Plus"
              >
                添加分块
              </el-button>
              <el-button
                size="small"
                @click="refreshChunks"
                :loading="resultLoading"
                :icon="Refresh"
              >
                刷新
              </el-button>
            </div>
          </div>

          <!-- 加载状态 -->
          <div v-if="resultLoading" class="loading-container">
            <el-skeleton :rows="3" animated />
            <div class="loading-text">正在加载分块数据...</div>
          </div>

          <!-- 错误状态 -->
          <div v-else-if="resultError" class="error-container">
            <el-alert
              :title="resultError"
              type="warning"
              :closable="false"
              show-icon
            />
          </div>

          <!-- 分块列表 -->
          <div v-else-if="resultChunks.length > 0" class="chunk-list">
            <!-- 调试信息 -->
            <div class="debug-info" style="background: #f0f9ff; padding: 8px; margin-bottom: 16px; border-radius: 4px; font-size: 12px; color: #1890ff;">
              📊 调试信息: 共 {{ resultChunks.length }} 个分块
              <span v-if="selectedChunks.size > 0" style="margin-left: 16px; color: #f56c6c;">
                已选中 {{ selectedChunks.size }} 个分块
              </span>
            </div>

            <!-- 分块操作提示 -->
            <div class="chunk-operation-notice">
              <el-alert
                title="分块操作说明"
                type="info"
                :closable="false"
                show-icon
              >
                <template #default>
                  <div class="operation-tips">
                    <p>• 支持操作：<strong>添加分块</strong>、<strong>修改分块内容</strong>、<strong>删除分块</strong></p>
                    <p>• 暂不支持：在指定位置插入分块（API限制）</p>
                    <p>• 替代方案：如需调整分块位置，可先删除再重新添加</p>
                  </div>
                </template>
              </el-alert>
            </div>

            <div
              v-for="(chunk, index) in resultChunks"
              :key="chunk.id || index"
              :class="[
                'chunk-item',
                {
                  'chunk-editing': editingChunks.has(chunk.id || ''),
                  'chunk-selected': selectedChunks.has(chunk.id || ''),
                  'chunk-select-mode': isSelectMode
                }
              ]"
            >
              <!-- 选择框 -->
              <div v-if="isSelectMode" class="chunk-selector">
                <el-checkbox
                  :model-value="selectedChunks.has(chunk.id || '')"
                  @change="toggleChunkSelection(chunk.id || '')"
                />
              </div>

              <div class="chunk-header">
                <div class="chunk-title">
                  <span class="chunk-index">分块 {{ index + 1 }}</span>
                  <div class="chunk-meta">
                    <span v-if="chunk.token_count" class="chunk-tokens">
                      {{ chunk.token_count }} tokens
                    </span>
                    <span v-if="chunk.similarity" class="chunk-similarity">
                      相似度: {{ (chunk.similarity * 100).toFixed(1) }}%
                    </span>
                  </div>
                </div>

                <!-- 操作按钮 -->
                <div v-if="!isSelectMode" class="chunk-actions">
                  <template v-if="editingChunks.has(chunk.id || '')">
                    <!-- 编辑模式按钮 -->
                    <el-button
                      size="small"
                      type="primary"
                      @click="saveEditChunk(chunk)"
                      :loading="chunkOperationLoading"
                      :icon="Check"
                    >
                      保存
                    </el-button>
                    <el-button
                      size="small"
                      @click="cancelEditChunk(chunk)"
                      :icon="Close"
                    >
                      取消
                    </el-button>
                  </template>
                  <template v-else>
                    <!-- 普通模式按钮 -->
                    <el-button
                      size="small"
                      @click="startEditChunk(chunk)"
                      :icon="Edit"
                    >
                      编辑
                    </el-button>
                    <el-button
                      size="small"
                      type="danger"
                      @click="deleteChunk(chunk)"
                      :loading="chunkOperationLoading"
                      :icon="Delete"
                    >
                      删除
                    </el-button>
                  </template>
                </div>
              </div>

              <!-- 分块内容 -->
              <div class="chunk-content">
                <template v-if="editingChunks.has(chunk.id || '')">
                  <!-- 编辑模式 -->
                  <el-input
                    :model-value="editingContents.get(chunk.id || '') || ''"
                    @update:model-value="(value: string) => handleContentChange(chunk.id || '', value)"
                    type="textarea"
                    :rows="6"
                    placeholder="请输入分块内容..."
                    class="chunk-editor"
                    show-word-limit
                    :maxlength="2000"
                  />
                  <div class="edit-stats">
                    <span>字符数: {{ (editingContents.get(chunk.id || '') || '').length }}</span>
                    <span>预估Token: {{ Math.ceil((editingContents.get(chunk.id || '') || '').length / 2) }}</span>
                    <span v-if="savingChunks.has(chunk.id || '')" class="saving-indicator">
                      <el-icon class="is-loading"><Loading /></el-icon>
                      正在保存...
                    </span>
                    <span v-else-if="autoSaveTimers.has(chunk.id || '')" class="auto-save-indicator">
                      <el-icon><Clock /></el-icon>
                      2秒后自动保存
                    </span>
                  </div>
                </template>
                <template v-else>
                  <!-- 显示模式 -->
                  <div v-if="chunk.highlight" v-html="chunk.highlight"></div>
                  <div v-else>{{ chunk.content || '内容为空' }}</div>
                </template>
              </div>

              <!-- 关键词 -->
              <div v-if="chunk.important_keywords?.length" class="chunk-keywords">
                <span class="keywords-label">关键词:</span>
                <el-tag
                  v-for="keyword in chunk.important_keywords"
                  :key="keyword"
                  size="small"
                  class="keyword-tag"
                >
                  {{ keyword }}
                </el-tag>
              </div>
            </div>

            <!-- 添加分块按钮 -->
            <div class="chunk-add-area">
              <el-button
                size="small"
                type="primary"
                @click="showAddChunkDialog()"
                :icon="Plus"
                class="add-button"
              >
                添加新分块
              </el-button>
            </div>
          </div>

          <!-- 空状态 -->
          <div v-else class="empty-chunks">
            <el-empty description="暂无分块数据" />
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 新增分块对话框 -->
    <el-dialog
      v-model="addChunkDialogVisible"
      title="添加新分块"
      width="60%"
      :close-on-click-modal="false"
    >
      <!-- API限制说明 -->
      <el-alert
        title="分块添加说明"
        type="info"
        :closable="false"
        show-icon
        style="margin-bottom: 16px;"
      >
        <template #default>
          <div>
            <p>• 新分块将添加到文档末尾（API暂不支持指定位置插入）</p>
            <p>• 如需调整分块顺序，可先删除相关分块再重新添加</p>
          </div>
        </template>
      </el-alert>

      <el-form label-width="100px">
        <el-form-item label="分块内容" required>
          <el-input
            v-model="newChunkContent"
            type="textarea"
            :rows="8"
            placeholder="请输入分块内容..."
            show-word-limit
            :maxlength="2000"
          />
          <div class="form-help">
            <span>字符数: {{ newChunkContent.length }}</span>
            <span style="margin-left: 16px;">预估Token: {{ Math.ceil(newChunkContent.length / 2) }}</span>
          </div>
        </el-form-item>

        <el-form-item label="关键词">
          <el-select
            v-model="newChunkKeywords"
            multiple
            filterable
            allow-create
            placeholder="输入关键词并按回车添加"
            style="width: 100%"
          >
          </el-select>
          <div class="form-help">
            可选：为分块添加重要关键词，有助于提高检索准确性
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="addChunkDialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="createNewChunk"
            :loading="chunkOperationLoading"
            :disabled="!newChunkContent.trim()"
          >
            创建分块
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  Refresh,
  VideoPlay,
  VideoPause,
  Search,
  Document,
  Grid,
  Coin,
  Warning,
  View,
  Download,
  ArrowDown,
  Edit,
  Delete,
  Plus,
  Check,
  Close,
  Select,
  CopyDocument,
  Loading,
  Clock
} from '@element-plus/icons-vue';

import {
  getDocumentList,
  startDocumentParsing,
  stopDocumentParsing,
  getParserOptions,
  formatFileSize,
  getDocumentChunks,
  createDocumentChunk,
  updateDocumentChunk,
  deleteDocumentChunks,
  type DocumentInfo,
  type DocumentChunk,
  type DocumentChunksQueryParams,
  type DocumentChunkCreateRequest,
  type DocumentChunkUpdateRequest,
  type DocumentChunkDeleteRequest
} from '/@/api/iot/document';

// Props
interface Props {
  knowledgeBaseId: string;
  documents?: DocumentInfo[];
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  statusChange: [documents: DocumentInfo[]];
  parseComplete: [document: DocumentInfo];
  parseError: [document: DocumentInfo, error: string];
}>();

// 响应式数据
const refreshing = ref(false);
const documents = ref<DocumentInfo[]>([]);
const statusFilter = ref('');
const searchKeyword = ref('');

// 轮询相关状态
const pollingTimer = ref<NodeJS.Timeout | null>(null);
const pollingInterval = 10000; // 轮询间隔10秒
const isPolling = ref(false);

// 批量操作
const batchStarting = ref(false);
const batchStopping = ref(false);

// 对话框状态
const configDialogVisible = ref(false);
const logDialogVisible = ref(false);
const resultDialogVisible = ref(false);
const configSaving = ref(false);
const logLoading = ref(false);

// 当前操作的文档
const currentDoc = ref<DocumentInfo | null>(null);
const currentResult = ref<DocumentInfo | null>(null);

// 解析配置
const parseConfig = reactive({
  parser_id: 'naive',
  chunk_token_count: 128,
  layout_recognize: true,
  delimiter: '\\n!?。；！？',
  task_page_size: 12
});

// 解析日志和结果
const parseLog = ref('');
const resultChunks = ref<DocumentChunk[]>([]);
const resultLoading = ref(false);
const resultError = ref('');

// 分块编辑相关
const editingChunks = ref<Set<string>>(new Set()); // 正在编辑的分块ID集合
const editingContents = ref<Map<string, string>>(new Map()); // 编辑中的内容
const originalContents = ref<Map<string, string>>(new Map()); // 原始内容备份
const selectedChunks = ref<Set<string>>(new Set()); // 选中的分块ID集合
const isSelectMode = ref(false); // 是否处于选择模式
const chunkOperationLoading = ref(false); // 分块操作加载状态

// 自动保存相关
const autoSaveTimers = ref<Map<string, NodeJS.Timeout>>(new Map()); // 自动保存定时器
const savingChunks = ref<Set<string>>(new Set()); // 正在保存的分块ID集合

// 新增分块对话框
const addChunkDialogVisible = ref(false);
const newChunkContent = ref('');
const newChunkKeywords = ref<string[]>([]);

// 批量操作
const batchOperationLoading = ref(false);

// 选项数据
const statusOptions = [
  { label: '已上传', value: 'uploaded' },
  { label: '解析中', value: 'parsing' },
  { label: '已解析', value: 'parsed' },
  { label: '失败', value: 'failed' }
];

const parserOptions = getParserOptions();

// 计算属性
const statusStats = computed(() => {
  const stats = {
    total: documents.value.length,
    uploaded: 0,
    parsing: 0,
    parsed: 0,
    failed: 0
  };
  
  documents.value.forEach(doc => {
    if (doc.status) {
      stats[doc.status as keyof typeof stats]++;
    }
  });
  
  return stats;
});

const filteredDocuments = computed(() => {
  let filtered = documents.value;
  
  // 状态筛选
  if (statusFilter.value) {
    filtered = filtered.filter(doc => doc.status === statusFilter.value);
  }
  
  // 关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase();
    filtered = filtered.filter(doc => 
      doc.name.toLowerCase().includes(keyword)
    );
  }
  
  return filtered;
});

const canStartBatch = computed(() => {
  return documents.value.some(doc => 
    doc.status === 'uploaded' || doc.status === 'failed'
  );
});

const canStopBatch = computed(() => {
  return documents.value.some(doc => doc.status === 'parsing');
});

// 方法
const loadDocuments = async () => {
  if (!props.knowledgeBaseId) return;
  
  try {
    const response = await getDocumentList({
      kb_id: props.knowledgeBaseId,
      page: 1,
      page_size: 100 // 获取文档列表（受后端限制）
    });

    const businessData = response.data;

    if (businessData.code === 200) {
      // 转换RAGFlow数据格式到前端期望格式
      const rawDocs = businessData.data?.docs || [];
      documents.value = rawDocs.map((doc: any) => ({
        ...doc,
        chunk_num: doc.chunk_count,
        token_num: doc.token_count,
        status: mapRAGFlowStatus(doc.run, doc.status),
        kb_id: props.knowledgeBaseId // 添加知识库ID
      }));
      emit('statusChange', documents.value);
    }
  } catch (error) {
    console.error('Load documents error:', error);
  }
};

const refreshStatus = async () => {
  refreshing.value = true;
  try {
    await loadDocuments();
  } finally {
    refreshing.value = false;
  }
};

// 轮询相关函数
const startPolling = () => {
  if (isPolling.value || pollingTimer.value) return;

  isPolling.value = true;
  pollingTimer.value = setInterval(() => {
    // 只有当有文档正在解析时才轮询
    const hasParsingDocs = documents.value.some(doc => doc.status === 'parsing');
    if (hasParsingDocs) {
      loadDocuments();
    } else {
      // 没有解析中的文档时停止轮询
      stopPolling();
    }
  }, pollingInterval);
};

const stopPolling = () => {
  if (pollingTimer.value) {
    clearInterval(pollingTimer.value);
    pollingTimer.value = null;
  }
  isPolling.value = false;
};

const handleFilterChange = () => {
  // 筛选变化处理
};

const handleSearch = () => {
  // 搜索处理
};

// 解析操作
const startParsing = async (doc: DocumentInfo) => {
  if (!doc.id) return;
  
  doc.starting = true;
  try {
    const response = await startDocumentParsing({
      kb_id: props.knowledgeBaseId,
      doc_id: doc.id,
      parser_id: doc.parser_id || 'naive'
    });
    
    if (response.data.code === 200) {
      ElMessage.success(`开始解析文档: ${doc.name}`);
      doc.status = 'parsing';
      doc.progress = 0;
      await loadDocuments();
      // 开始解析后立即启动轮询
      startPolling();
    } else {
      ElMessage.error(response.data.msg || '开始解析失败');
    }
  } catch (error) {
    ElMessage.error('开始解析失败');
    emit('parseError', doc, error instanceof Error ? error.message : '解析失败');
  } finally {
    doc.starting = false;
  }
};

const stopParsing = async (doc: DocumentInfo) => {
  if (!doc.id) return;
  
  doc.stopping = true;
  try {
    const response = await stopDocumentParsing(props.knowledgeBaseId, doc.id);
    
    if (response.data.code === 200) {
      ElMessage.success(`停止解析文档: ${doc.name}`);
      doc.status = 'cancelled';
      await loadDocuments();
    } else {
      ElMessage.error(response.data.msg || '停止解析失败');
    }
  } catch (error) {
    ElMessage.error('停止解析失败');
  } finally {
    doc.stopping = false;
  }
};

const startBatchParsing = async () => {
  const pendingDocs = documents.value.filter(doc => 
    doc.status === 'uploaded' || doc.status === 'failed'
  );
  
  if (pendingDocs.length === 0) {
    ElMessage.warning('没有可解析的文档');
    return;
  }
  
  try {
    await ElMessageBox.confirm(
      `确定要开始解析 ${pendingDocs.length} 个文档吗？`,
      '批量解析确认',
      {
        confirmButtonText: '开始解析',
        cancelButtonText: '取消',
        type: 'info'
      }
    );
    
    batchStarting.value = true;
    
    for (const doc of pendingDocs) {
      await startParsing(doc);
    }
    
    ElMessage.success(`开始批量解析 ${pendingDocs.length} 个文档`);
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量解析失败');
    }
  } finally {
    batchStarting.value = false;
  }
};

const stopBatchParsing = async () => {
  const parsingDocs = documents.value.filter(doc => doc.status === 'parsing');
  
  if (parsingDocs.length === 0) {
    ElMessage.warning('没有正在解析的文档');
    return;
  }
  
  try {
    await ElMessageBox.confirm(
      `确定要停止 ${parsingDocs.length} 个正在解析的文档吗？`,
      '停止解析确认',
      {
        confirmButtonText: '停止解析',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );
    
    batchStopping.value = true;
    
    for (const doc of parsingDocs) {
      await stopParsing(doc);
    }
    
    ElMessage.success(`停止 ${parsingDocs.length} 个文档的解析`);
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量停止失败');
    }
  } finally {
    batchStopping.value = false;
  }
};

const handleAction = (command: string, doc: DocumentInfo) => {
  currentDoc.value = doc;
  
  switch (command) {
    case 'reparse':
      startParsing(doc);
      break;
    case 'config':
      openConfigDialog(doc);
      break;
    case 'log':
      openLogDialog(doc);
      break;
    case 'delete':
      // 删除文档逻辑
      break;
  }
};

const openConfigDialog = (doc: DocumentInfo) => {
  currentDoc.value = doc;
  parseConfig.parser_id = doc.parser_id || 'naive';
  configDialogVisible.value = true;
};

const saveConfig = async () => {
  if (!currentDoc.value?.id) return;
  
  configSaving.value = true;
  try {
    const response = await startDocumentParsing({
      kb_id: props.knowledgeBaseId,
      doc_id: currentDoc.value.id,
      parser_id: parseConfig.parser_id,
      parser_config: {
        chunk_token_count: parseConfig.chunk_token_count,
        layout_recognize: parseConfig.layout_recognize,
        delimiter: parseConfig.delimiter,
        task_page_size: parseConfig.task_page_size
      }
    });
    
    if (response.data.code === 200) {
      ElMessage.success('保存配置并开始解析');
      configDialogVisible.value = false;
      await loadDocuments();
    } else {
      ElMessage.error(response.data.msg || '保存配置失败');
    }
  } catch (error) {
    ElMessage.error('保存配置失败');
  } finally {
    configSaving.value = false;
  }
};

const openLogDialog = (doc: DocumentInfo) => {
  currentDoc.value = doc;
  logDialogVisible.value = true;
  refreshLog();
};

const refreshLog = async () => {
  if (!currentDoc.value?.id) return;
  
  logLoading.value = true;
  try {
    // 模拟获取解析日志
    parseLog.value = `解析日志 - ${currentDoc.value.name}\n\n` +
      `开始时间: ${new Date().toLocaleString()}\n` +
      `解析器: ${currentDoc.value.parser_id}\n` +
      `状态: ${currentDoc.value.status}\n` +
      `进度: ${Math.round((currentDoc.value.progress || 0) * 100)}%\n\n` +
      `详细日志:\n` +
      `[INFO] 开始解析文档\n` +
      `[INFO] 文档类型检测完成\n` +
      `[INFO] 开始文本提取\n` +
      `[INFO] 文本分块处理\n` +
      `[INFO] 向量化处理\n` +
      `[SUCCESS] 解析完成`;
  } catch (error) {
    ElMessage.error('获取日志失败');
  } finally {
    logLoading.value = false;
  }
};

const downloadLog = () => {
  if (!parseLog.value) return;
  
  const blob = new Blob([parseLog.value], { type: 'text/plain' });
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = `parse-log-${currentDoc.value?.name || 'document'}.txt`;
  link.click();
  URL.revokeObjectURL(url);
};

const viewResult = async (doc: DocumentInfo) => {
  currentResult.value = doc;
  resultDialogVisible.value = true;
  await loadDocumentChunks(doc);
};

// 加载文档分块数据
const loadDocumentChunks = async (doc: DocumentInfo) => {
  resultLoading.value = true;
  resultError.value = '';
  resultChunks.value = [];

  try {
    // 获取文档的分块数据
    const params: DocumentChunksQueryParams = {
      dataset_id: doc.dataset_id || doc.kb_id || '',
      document_id: doc.id || '',
      page: 1,
      page_size: 50 // 获取前50个分块
    };

    console.log('🔍 获取文档分块参数:', params);
    const response = await getDocumentChunks(params);
    console.log('📡 API响应原始数据:', response);

    // 修复：axios响应结构是 { data: { code, msg, data } }
    const businessData = response.data as any;
    console.log('📊 业务数据:', businessData);

    if (businessData && businessData.code === 200 && businessData.data) {
      resultChunks.value = businessData.data.chunks || [];
      console.log('✅ 成功获取分块数据:', resultChunks.value.length, '个分块');
      console.log('📋 分块数据详情:', resultChunks.value);

      // 如果没有获取到分块数据，显示提示信息
      if (resultChunks.value.length === 0) {
        resultError.value = '该文档暂无分块数据，可能解析尚未完成或解析失败';
      } else {
        // 清除错误信息
        resultError.value = '';
        // 验证每个分块的内容
        resultChunks.value.forEach((chunk, index) => {
          console.log(`📄 分块 ${index + 1}:`, {
            id: chunk.id,
            content: chunk.content ? chunk.content.substring(0, 50) + '...' : '无内容',
            token_count: chunk.token_count,
            hasContent: !!chunk.content
          });
        });
      }
    } else {
      console.log('❌ 业务逻辑失败:', businessData);
      resultError.value = businessData?.msg || businessData?.message || '获取解析结果失败';
    }
  } catch (error: any) {
    console.error('获取文档分块失败:', error);
    resultError.value = error.message || '获取解析结果失败，请稍后重试';
  } finally {
    resultLoading.value = false;
  }
};

// ==================== 分块编辑功能方法 ====================

/**
 * 处理内容变化（支持自动保存）
 */
const handleContentChange = (chunkId: string, value: string) => {
  if (!chunkId) return;

  // 更新编辑内容
  editingContents.value.set(chunkId, value);

  // 清除之前的自动保存定时器
  const existingTimer = autoSaveTimers.value.get(chunkId);
  if (existingTimer) {
    clearTimeout(existingTimer);
  }

  // 设置新的自动保存定时器（2秒后自动保存）
  const timer = setTimeout(() => {
    autoSaveChunk(chunkId);
  }, 2000);

  autoSaveTimers.value.set(chunkId, timer);
};

/**
 * 自动保存分块（无感保存）
 */
const autoSaveChunk = async (chunkId: string) => {
  const chunk = resultChunks.value.find(c => c.id === chunkId);
  if (!chunk) return;

  const newContent = editingContents.value.get(chunkId);
  const originalContent = originalContents.value.get(chunkId);

  // 内容没有变化，不需要保存
  if (!newContent || newContent === originalContent) return;

  // 避免重复保存
  if (savingChunks.value.has(chunkId)) return;

  try {
    savingChunks.value.add(chunkId);

    const updateData: DocumentChunkUpdateRequest = {
      content: newContent,
      important_keywords: chunk.important_keywords || []
    };

    console.log(`💾 自动保存分块: ${chunkId}`);

    const response = await updateDocumentChunk(
      currentResult.value?.dataset_id || currentResult.value?.kb_id || '',
      currentResult.value?.id || '',
      chunkId,
      updateData
    );

    const businessData = response.data as any;
    const fbaSuccess = businessData && businessData.code === 200;

    if (fbaSuccess) {
      // 🎯 Vue响应式无感更新：直接更新响应式数据
      const chunkIndex = resultChunks.value.findIndex((c: DocumentChunk) => c.id === chunkId);
      if (chunkIndex !== -1) {
        resultChunks.value[chunkIndex].content = newContent;
        if (updateData.important_keywords) {
          resultChunks.value[chunkIndex].important_keywords = updateData.important_keywords;
        }
      }

      // 更新原始内容为当前内容（避免重复保存）
      originalContents.value.set(chunkId, newContent);

      console.log(`✅ 自动保存成功: ${chunkId}`);
    }
  } catch (error) {
    console.warn(`⚠️ 自动保存失败: ${chunkId}`, error);
  } finally {
    savingChunks.value.delete(chunkId);
    autoSaveTimers.value.delete(chunkId);
  }
};

/**
 * 开始编辑分块
 */
const startEditChunk = (chunk: DocumentChunk) => {
  const chunkId = chunk.id || '';
  if (!chunkId) return;

  // 保存原始内容
  originalContents.value.set(chunkId, chunk.content || '');
  // 设置编辑内容
  editingContents.value.set(chunkId, chunk.content || '');
  // 添加到编辑集合
  editingChunks.value.add(chunkId);

  console.log(`🖊️ 开始编辑分块: ${chunkId}`);
};

/**
 * 取消编辑分块
 */
const cancelEditChunk = (chunk: DocumentChunk) => {
  const chunkId = chunk.id || '';
  if (!chunkId) return;

  // 清除自动保存定时器
  const existingTimer = autoSaveTimers.value.get(chunkId);
  if (existingTimer) {
    clearTimeout(existingTimer);
    autoSaveTimers.value.delete(chunkId);
  }

  // 移除编辑状态
  editingChunks.value.delete(chunkId);
  editingContents.value.delete(chunkId);
  originalContents.value.delete(chunkId);
  savingChunks.value.delete(chunkId);

  console.log(`❌ 取消编辑分块: ${chunkId}`);
};

/**
 * 保存分块编辑
 */
const saveEditChunk = async (chunk: DocumentChunk) => {
  const chunkId = chunk.id || '';
  if (!chunkId) return;

  const newContent = editingContents.value.get(chunkId);
  const originalContent = originalContents.value.get(chunkId);

  if (!newContent || newContent === originalContent) {
    // 内容没有变化，直接取消编辑
    cancelEditChunk(chunk);
    return;
  }

  try {
    chunkOperationLoading.value = true;

    const updateData: DocumentChunkUpdateRequest = {
      content: newContent,
      important_keywords: chunk.important_keywords || []
    };

    console.log(`💾 保存分块编辑: ${chunkId}`, updateData);
    console.log(`📋 请求参数:`, {
      dataset_id: currentResult.value?.dataset_id || currentResult.value?.kb_id,
      document_id: currentResult.value?.id,
      chunk_id: chunkId
    });

    const response = await updateDocumentChunk(
      currentResult.value?.dataset_id || currentResult.value?.kb_id || '',
      currentResult.value?.id || '',
      chunkId,
      updateData
    );

    console.log(`📡 更新API响应:`, response);
    const businessData = response.data as any;
    console.log(`📊 更新业务数据:`, businessData);

    // 基于测试脚本的成功验证逻辑：检查FBA后台响应
    // FBA后台成功响应：businessData.code === 200
    const fbaSuccess = businessData && businessData.code === 200;

    // 检查RAGFlow响应（如果存在嵌套的RAGFlow响应数据）
    const ragflowData = businessData?.data;
    let ragflowSuccess = true; // 默认为成功

    // 只有当data存在且有code字段时才检查RAGFlow状态
    if (ragflowData && typeof ragflowData === 'object' && 'code' in ragflowData) {
      ragflowSuccess = ragflowData.code === 0;
    }
    // 如果data是空对象{}或者没有code字段，认为是成功的（FBA直接处理成功）

    console.log(`🔍 响应状态检查: FBA=${fbaSuccess}, RAGFlow=${ragflowSuccess}, data=`, ragflowData);

    if (fbaSuccess && ragflowSuccess) {
      // 🎯 Vue响应式无感更新：直接更新响应式数据，界面自动同步
      const chunkIndex = resultChunks.value.findIndex((c: DocumentChunk) => c.id === chunkId);
      if (chunkIndex !== -1) {
        // 直接修改响应式数组，Vue会自动更新界面
        resultChunks.value[chunkIndex].content = newContent;
        if (updateData.important_keywords) {
          resultChunks.value[chunkIndex].important_keywords = updateData.important_keywords;
        }
      }

      // 清除编辑状态（界面会自动退出编辑模式）
      cancelEditChunk(chunk);

      // 静默成功提示（可选，也可以完全无感）
      ElMessage({
        message: '内容已保存',
        type: 'success',
        duration: 1500,
        showClose: false
      });

      console.log(`✅ 分块内容已无感更新: ${chunkId}`);

      // 🔄 后台静默同步验证（不影响用户体验）
      setTimeout(async () => {
        try {
          // 静默刷新，不显示加载状态
          const currentLoading = resultLoading.value;
          resultLoading.value = false; // 确保不显示加载状态

          await refreshChunks();

          resultLoading.value = currentLoading; // 恢复原始加载状态
          console.log(`🔄 后台数据同步完成: ${chunkId}`);
        } catch (error) {
          console.warn('后台同步失败，但不影响用户操作:', error);
        }
      }, 2000);
    } else {
      // 处理各种错误情况
      let errorMessage = '更新失败';
      if (!fbaSuccess) {
        errorMessage = businessData?.msg || businessData?.message || 'FBA后台处理失败';
      } else if (!ragflowSuccess) {
        errorMessage = ragflowData?.message || ragflowData?.msg || 'RAGFlow服务处理失败';
      }
      throw new Error(errorMessage);
    }

  } catch (error: any) {
    console.error('保存分块编辑失败:', error);
    ElMessage.error(error.message || '保存失败，请稍后重试');
  } finally {
    chunkOperationLoading.value = false;
  }
};

/**
 * 删除分块
 */
const deleteChunk = async (chunk: DocumentChunk) => {
  const chunkId = chunk.id || '';
  if (!chunkId) return;

  try {
    await ElMessageBox.confirm(
      `确定要删除这个分块吗？删除后无法恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    );

    chunkOperationLoading.value = true;

    const deleteData: DocumentChunkDeleteRequest = {
      chunk_ids: [chunkId]
    };

    console.log(`🗑️ 删除分块: ${chunkId}`);

    const response = await deleteDocumentChunks(
      currentResult.value?.dataset_id || currentResult.value?.kb_id || '',
      currentResult.value?.id || '',
      deleteData
    );

    const businessData = response.data as any;

    if (businessData && businessData.code === 200) {
      // 从本地数据中移除
      const chunkIndex = resultChunks.value.findIndex(c => c.id === chunkId);
      if (chunkIndex !== -1) {
        resultChunks.value.splice(chunkIndex, 1);
      }

      // 清除相关状态
      editingChunks.value.delete(chunkId);
      editingContents.value.delete(chunkId);
      originalContents.value.delete(chunkId);
      selectedChunks.value.delete(chunkId);

      ElMessage.success('分块删除成功');
      console.log(`✅ 分块删除成功: ${chunkId}`);
    } else {
      throw new Error(businessData?.msg || businessData?.message || '删除失败');
    }

  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('删除分块失败:', error);
      ElMessage.error(error.message || '删除失败，请稍后重试');
    }
  } finally {
    chunkOperationLoading.value = false;
  }
};

/**
 * 显示新增分块对话框
 */
const showAddChunkDialog = () => {
  newChunkContent.value = '';
  newChunkKeywords.value = [];
  addChunkDialogVisible.value = true;
};

/**
 * 创建新分块
 */
const createNewChunk = async () => {
  if (!newChunkContent.value.trim()) {
    ElMessage.warning('请输入分块内容');
    return;
  }

  try {
    chunkOperationLoading.value = true;

    const createData: DocumentChunkCreateRequest = {
      content: newChunkContent.value.trim(),
      important_keywords: newChunkKeywords.value,
      questions: []
    };

    console.log(`➕ 创建新分块:`, createData);

    const response = await createDocumentChunk(
      currentResult.value?.dataset_id || currentResult.value?.kb_id || '',
      currentResult.value?.id || '',
      createData
    );

    const businessData = response.data as any;

    if (businessData && businessData.code === 200) {
      // 重新加载分块数据
      await refreshChunks();

      addChunkDialogVisible.value = false;
      ElMessage.success('分块创建成功');
      console.log(`✅ 分块创建成功`);
    } else {
      throw new Error(businessData?.msg || businessData?.message || '创建失败');
    }

  } catch (error: any) {
    console.error('创建分块失败:', error);
    ElMessage.error(error.message || '创建失败，请稍后重试');
  } finally {
    chunkOperationLoading.value = false;
  }
};

/**
 * 切换选择模式
 */
const toggleSelectMode = () => {
  isSelectMode.value = !isSelectMode.value;
  if (!isSelectMode.value) {
    selectedChunks.value.clear();
  }
};

/**
 * 切换分块选择状态
 */
const toggleChunkSelection = (chunkId: string) => {
  if (selectedChunks.value.has(chunkId)) {
    selectedChunks.value.delete(chunkId);
  } else {
    selectedChunks.value.add(chunkId);
  }
};

/**
 * 全选/取消全选
 */
const toggleSelectAll = () => {
  if (selectedChunks.value.size === resultChunks.value.length) {
    selectedChunks.value.clear();
  } else {
    selectedChunks.value.clear();
    resultChunks.value.forEach(chunk => {
      if (chunk.id) {
        selectedChunks.value.add(chunk.id);
      }
    });
  }
};

/**
 * 批量删除选中的分块
 */
const batchDeleteChunks = async () => {
  if (selectedChunks.value.size === 0) {
    ElMessage.warning('请先选择要删除的分块');
    return;
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedChunks.value.size} 个分块吗？删除后无法恢复。`,
      '批量删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    );

    batchOperationLoading.value = true;

    const deleteData: DocumentChunkDeleteRequest = {
      chunk_ids: Array.from(selectedChunks.value)
    };

    console.log(`🗑️ 批量删除分块:`, deleteData.chunk_ids);

    const response = await deleteDocumentChunks(
      currentResult.value?.dataset_id || currentResult.value?.kb_id || '',
      currentResult.value?.id || '',
      deleteData
    );

    const businessData = response.data as any;

    if (businessData && businessData.code === 200) {
      // 重新加载分块数据
      await refreshChunks();

      selectedChunks.value.clear();
      isSelectMode.value = false;

      ElMessage.success(`成功删除 ${deleteData.chunk_ids.length} 个分块`);
      console.log(`✅ 批量删除成功`);
    } else {
      throw new Error(businessData?.msg || businessData?.message || '批量删除失败');
    }

  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error);
      ElMessage.error(error.message || '批量删除失败，请稍后重试');
    }
  } finally {
    batchOperationLoading.value = false;
  }
};

// 刷新分块数据
const refreshChunks = async () => {
  if (currentResult.value) {
    await loadDocumentChunks(currentResult.value);
  }
};

// 工具函数
const getStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    uploaded: 'info',
    parsing: 'warning',
    parsed: 'success',
    failed: 'danger',
    cancelled: 'info'
  };
  return typeMap[status] || 'info';
};

const getStatusLabel = (status: string) => {
  const labelMap: Record<string, string> = {
    uploaded: '已上传',
    parsing: '解析中',
    parsed: '已解析',
    failed: '失败',
    cancelled: '已取消'
  };
  return labelMap[status] || status;
};

const getFileTypeLabel = (mimeType: string) => {
  const typeMap: Record<string, string> = {
    'application/pdf': 'PDF',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'Word',
    'text/plain': 'TXT'
  };
  return typeMap[mimeType] || '文档';
};

const getFileIconColor = (mimeType: string) => {
  if (mimeType?.includes('pdf')) return '#F56C6C';
  if (mimeType?.includes('word')) return '#409EFF';
  if (mimeType?.includes('text')) return '#909399';
  return '#409EFF';
};

const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-';
  return new Date(dateTime).toLocaleString('zh-CN');
};

const formatNumber = (num: number) => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M';
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K';
  }
  return num.toString();
};

// RAGFlow状态映射到前端状态
const mapRAGFlowStatus = (run: string, status: string) => {
  // run字段：RUNNING, DONE, FAIL, CANCEL
  // status字段：'0'(未开始), '1'(已完成), '2'(失败)
  if (run === 'RUNNING') return 'parsing';
  if (run === 'DONE' && status === '1') return 'parsed';
  if (run === 'FAIL' || status === '2') return 'failed';
  if (run === 'CANCEL') return 'cancelled';
  return 'uploaded'; // 默认状态
};

const calculateDuration = (doc: DocumentInfo | null) => {
  if (!doc?.create_time || !doc?.update_time) return '-';
  
  const start = new Date(doc.create_time);
  const end = new Date(doc.update_time);
  const duration = end.getTime() - start.getTime();
  
  const minutes = Math.floor(duration / 60000);
  const seconds = Math.floor((duration % 60000) / 1000);
  
  return `${minutes}分${seconds}秒`;
};

// 生命周期
onMounted(() => {
  loadDocuments();
  // 检查是否有正在解析的文档，如果有则启动轮询
  const hasParsingDocs = documents.value.some(doc => doc.status === 'parsing');
  if (hasParsingDocs) {
    startPolling();
  }
});

onUnmounted(() => {
  // 组件卸载时停止轮询
  stopPolling();

  // 清理所有自动保存定时器
  autoSaveTimers.value.forEach((timer) => {
    clearTimeout(timer);
  });
  autoSaveTimers.value.clear();
});

// 监听知识库ID变化
watch(() => props.knowledgeBaseId, () => {
  if (props.knowledgeBaseId) {
    loadDocuments();
  }
});

// 监听文档状态变化，自动管理轮询
watch(() => documents.value, (newDocs) => {
  const hasParsingDocs = newDocs.some(doc => doc.status === 'parsing');
  if (hasParsingDocs && !isPolling.value) {
    startPolling();
  } else if (!hasParsingDocs && isPolling.value) {
    stopPolling();
  }
}, { deep: true });

// 暴露方法
defineExpose({
  refreshStatus,
  startBatchParsing,
  stopBatchParsing
});
</script>

<style scoped>
.document-parse-status-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.status-stats {
  display: flex;
  gap: 24px;
  justify-content: space-around;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.stat-value.success { color: #67C23A; }
.stat-value.warning { color: #E6A23C; }
.stat-value.info { color: #409EFF; }
.stat-value.danger { color: #F56C6C; }

.stat-label {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.filter-bar {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.progress-list {
  max-height: 600px;
  overflow-y: auto;
}

.progress-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.3s;
}

.progress-item:hover {
  background-color: #f8f9fa;
}

.progress-item.parsing {
  background-color: #fff7e6;
  border-left: 3px solid #E6A23C;
}

.document-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  min-width: 200px;
}

.document-details {
  flex: 1;
  min-width: 0;
}

.document-name {
  font-weight: 500;
  color: #303133;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.document-meta {
  font-size: 12px;
  color: #909399;
  margin-top: 2px;
}

.parse-info {
  flex: 2;
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin: 0 20px;
}

.status-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.progress-section {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.progress-text {
  font-size: 12px;
  color: #606266;
  min-width: 30px;
}

.result-section {
  display: flex;
  gap: 16px;
}

.result-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #606266;
}

.error-section {
  display: flex;
  align-items: center;
  gap: 8px;
}

.error-text {
  font-size: 12px;
  color: #F56C6C;
  max-width: 200px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.time-section {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.time-item {
  font-size: 11px;
  color: #909399;
}

.time-label {
  margin-right: 4px;
}

.action-section {
  display: flex;
  gap: 8px;
  align-items: center;
}

.empty-state {
  padding: 40px 0;
}

.form-hint {
  font-size: 12px;
  color: #909399;
  margin-left: 8px;
}

.log-container {
  display: flex;
  flex-direction: column;
  height: 500px;
}

.log-header {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.log-content {
  flex: 1;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
}

.log-text {
  padding: 16px;
  margin: 0;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.5;
  background: #f8f9fa;
  color: #303133;
  height: 100%;
  overflow: auto;
}

/* 删除重复的样式定义，使用下面统一的样式 */

/* 解析结果对话框样式 */
.result-container {
  max-height: 70vh;
  overflow-y: auto;
}

.result-summary {
  margin-bottom: 24px;
}

.result-chunks {
  margin-top: 16px;
}

.chunks-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.chunks-header h4 {
  margin: 0;
  color: #303133;
  font-size: 16px;
}

.chunks-actions {
  display: flex;
  gap: 8px;
}

.loading-container {
  padding: 20px;
  text-align: center;
}

.loading-text {
  margin-top: 12px;
  color: #909399;
  font-size: 14px;
}

.error-container {
  margin: 16px 0;
}

.chunk-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  max-height: 500px;
  overflow-y: auto;
  padding: 8px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  background-color: #fafafa;
}

.chunk-item {
  border: 1px solid #EBEEF5;
  border-radius: 8px;
  padding: 16px;
  background-color: #FFFFFF;
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.chunk-item:hover {
  border-color: #409EFF;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15);
  transform: translateY(-1px);
}

.chunk-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #E4E7ED;
}

.chunk-index {
  font-weight: 600;
  color: #409EFF;
  font-size: 14px;
}

.chunk-meta {
  display: flex;
  gap: 12px;
  align-items: center;
}

.chunk-tokens {
  font-size: 12px;
  color: #67C23A;
  background-color: #F0F9FF;
  padding: 4px 8px;
  border-radius: 12px;
  border: 1px solid #E1F5FE;
  font-weight: 500;
}

.chunk-similarity {
  font-size: 12px;
  color: #E6A23C;
  background-color: #FDF6EC;
  padding: 4px 8px;
  border-radius: 12px;
  border: 1px solid #FAECD8;
  font-weight: 500;
}

.chunk-content {
  line-height: 1.6;
  color: #303133;
  font-size: 14px;
  white-space: pre-wrap;
  word-break: break-word;
  min-height: 60px;
  max-height: 300px;
  overflow-y: auto;
  padding: 12px;
  background-color: #F8F9FA;
  border-radius: 6px;
  border: 1px solid #E9ECEF;
  margin: 8px 0;
}

.chunk-keywords {
  margin-top: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.keywords-label {
  font-size: 12px;
  color: #909399;
  font-weight: 500;
}

.keyword-tag {
  margin-right: 4px;
  margin-bottom: 4px;
}

.empty-chunks {
  text-align: center;
  padding: 40px 20px;
}

/* ==================== 分块编辑功能样式 ==================== */

.chunk-insert-area {
  text-align: center;
  padding: 16px 0;
  border: 2px dashed #E4E7ED;
  border-radius: 6px;
  margin: 8px 0;
  transition: all 0.3s ease;
}

.chunk-insert-area:hover {
  border-color: #409EFF;
  background-color: #F0F9FF;
}

.insert-button {
  color: #409EFF;
  font-size: 14px;
}

.chunk-item.chunk-editing {
  border-color: #409EFF;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
  background-color: #F0F9FF;
}

.chunk-item.chunk-selected {
  border-color: #67C23A;
  box-shadow: 0 0 0 2px rgba(103, 194, 58, 0.1);
  background-color: #F0F9FF;
}

.chunk-item.chunk-select-mode {
  padding-left: 50px;
  position: relative;
}

.chunk-selector {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
}

.chunk-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #E4E7ED;
}

.chunk-title {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chunk-actions {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
  margin-left: 16px;
}

.chunk-editor {
  margin-bottom: 8px;
}

.edit-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #909399;
  padding: 4px 8px;
  background-color: #F5F7FA;
  border-radius: 4px;
  margin-top: 8px;
  flex-wrap: wrap;
  gap: 8px;
}

.saving-indicator {
  color: #409EFF;
  display: flex;
  align-items: center;
  gap: 4px;
  font-weight: 500;
}

.auto-save-indicator {
  color: #E6A23C;
  display: flex;
  align-items: center;
  gap: 4px;
}

.chunks-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.form-help {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.dialog-footer {
  text-align: right;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chunk-actions {
    flex-direction: column;
    gap: 4px;
  }

  .chunks-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .chunk-title {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .chunk-meta {
    align-self: flex-start;
  }
}

@media (max-width: 768px) {
  .status-stats {
    flex-wrap: wrap;
    gap: 16px;
  }

  .progress-item {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .parse-info {
    margin: 0;
  }

  .filter-bar {
    flex-direction: column;
  }

  .chunk-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .chunk-meta {
    align-self: flex-end;
  }
}
</style>
