# Git状态汇总报告

## 📊 3个仓库概览

| 仓库名称 | 分支 | 状态 | 待提交文件数 | 未暂存文件数 | 未跟踪文件数 |
|---------|------|------|-------------|-------------|-------------|
| fastapi_best_architecture | dev-wjj | ✅ 最新 | 18 | 6 | 6 |
| TS-IOT-SYS-WEBUI | Dev-Wjj | ✅ 最新 | 6 | 4 | 3 |
| TS-IOT-SYS-Service | main | ⚠️ 落后16个提交 | 0 | 6 | 1 |

---

## 🔧 仓库1: fastapi_best_architecture (FastAPI后端)

**分支**: `dev-wjj`  
**状态**: 与 `origin/dev-wjj` 保持最新

### 📝 Changes to be committed (待提交的文件)
```
.github/workflows/README.md
.gitignore (modified)
01文件预览修改汇总.md
05.md
CRITICAL_INFO_AND_TASKS.md
CRITICAL_INFO_QUICK_INDEX.md
RAGFlow集成项目综合指南.md
docs/commits/2025-08-06-code-review-report.md
docs/commits/2025-08-06-iot-knowledge-base-integration.md
docs/knowledge_base_config.md
docs/权限系统技术讲解最终版.md
ragflowapi.md
scripts/permission_testing/README.md
scripts/permission_testing/test_getinfo_api.py
scripts/permission_testing/test_java_routers.py
test_chunk_update.py
test_upload.pptx
test_upload.txt
权限控制系统调研报告.md
用户权限配置问题诊断分析.md
```

### 🔄 Changes not staged for commit (未暂存的修改)
```
CRITICAL_INFO_AND_TASKS.md (modified)
RAGFlow集成项目综合指南.md (modified)
backend/app/iot/api/v1/router.py (modified)
backend/app/iot/service/knowledge_base_service.py (modified)
backend/core/conf.py (modified)
backend/middleware/opera_log_middleware.py (modified)
```

### 📁 Untracked files (未跟踪的文件)
```
backend/app/iot/api/v1/document.py
backend/app/iot/schema/document.py
backend/app/iot/service/document_service.py
backend/app/iot/utils/
ragflow_openapi.json
scripts/permission_testing/check_thingsmodel_permissions.py
```

---

## 🎨 仓库2: TS-IOT-SYS-WEBUI (前端UI)

**分支**: `Dev-Wjj`  
**状态**: 与 `origin/Dev-Wjj` 保持最新

### 📝 Changes to be committed (待提交的文件)
```
.env.development (modified)
.env.production (modified)
.gitignore (modified)
DOCUMENT_PREVIEW_REFACTOR_LOG.md
Git协作开发完整流程指南.md
priority_sorting_test.md
```

### 🔄 Changes not staged for commit (未暂存的修改)
```
package-lock.json (modified)
package.json (modified)
src/api/iot/knowledgeBase.ts (modified)
src/views/ai/kb/fm/index.vue (modified)
```

### 📁 Untracked files (未跟踪的文件)
```
src/api/iot/document.ts
src/components/FileManagement/
src/components/FileUpload/DocumentUpload.vue
```

---

## ⚙️ 仓库3: TS-IOT-SYS-Service (IoT服务)

**分支**: `main`  
**状态**: ⚠️ 落后 `origin/main` 16个提交 (需要执行 `git pull`)

### 📝 Changes to be committed (待提交的文件)
```
无
```

### 🔄 Changes not staged for commit (未暂存的修改)
```
.gitignore (modified)
docker/DockerData/ts-iot-sys/docker-compose.yml (modified)
docker/DockerData/ts-iot-sys/java/resources/application-prod.yml (modified)
fastbee-admin/src/main/resources/application-dev.yml (modified)
fastbee-admin/src/main/resources/application.yml (modified)
fastbee-service/fastbee-iot-service/src/main/java/com/fastbee/device/controller/IotThingsModelController.java (modified)
```

### 📁 Untracked files (未跟踪的文件)
```
.vscode/
```

---

## 🚨 需要注意的问题

1. **TS-IOT-SYS-Service** 仓库落后远程分支16个提交，建议先执行 `git pull` 更新
2. **fastapi_best_architecture** 仓库有大量待提交的文档和测试文件
3. **TS-IOT-SYS-WEBUI** 仓库有新的文件管理组件未跟踪

## 📋 建议操作

1. 对于 **TS-IOT-SYS-Service**: 先 `git pull` 更新，然后处理冲突
2. 对于 **fastapi_best_architecture**: 检查待提交文件，确认后提交
3. 对于 **TS-IOT-SYS-WEBUI**: 添加新组件到版本控制

---

*报告生成时间: 2025-08-22*
